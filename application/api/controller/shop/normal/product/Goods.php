<?php


namespace app\api\controller\shop\normal\product;

use app\api\library\ShopHandle;
use app\common\controller\Api;
use Exception;

class Goods extends Api
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

    /**
     * @return void
     * 首页 普通商品信息
     * @throws \Throwable
     */
    public function Lists($ids = null)
    {
        // 查询缓存
        $cacheTime = 60;

        // Get category IDs
        // 1. 获取分类ID列表（优化：直接获取ID数组，减少内存占用）
        $categoryIds = ShopHandle::instance()->getCategoryIds($cacheTime);

        if (empty($categoryIds)) {
            $this->success('ok', []);
            return;
        }

        // 2. 获取商品列表（优化：使用关联查询避免N+1问题）
        $goodsData = ShopHandle::instance()->getGoodsData($categoryIds, $cacheTime);
        if (empty($goodsData)) {
            $this->success('ok', []);
            return;
        }

        // 3. 获取店铺数据
        $shopData = ShopHandle::instance()->getShopData($goodsData, $cacheTime);

        // 4. 获取直播数据
        $liveData = ShopHandle::instance()->getLiveData($goodsData, $cacheTime);

        // 5. 组装最终数据
        $goodsList = ShopHandle::instance()->assembleData($goodsData, $shopData, $liveData);

        $this->success('ok', $goodsList);
    }
}
<?php

namespace app\api\controller\shop\redeem\product;

use addons\wanlshop\library\WanlSdk\Common;
use app\api\library\CommonHandle;
use app\api\library\GoodsHandle;
use app\api\library\OrderHandle;
use app\common\controller\Api;
use think\Db;

class Order extends Api
{
    protected $noNeedLogin = [];
    protected $noNeedRight = ['*'];

    /**
     * 获取兑换卡订单信息
     * @throws \Exception
     */
    public function Details()
    {
        if ($this->request->isPost()) {
            //设置过滤方法
            $this->request->filter(['strip_tags']);
            $params = $this->request->post();

            CommonHandle::instance()->checkRequiredKey($params, 'data', '非法请求');

            // 地址id
            $addressId = $params['address_id'] ?? '';
            // 修复客户端地址更新
            $where = !empty($addressId)
                ? ['id' => $addressId, 'user_id' => $this->auth->id]
                : ['user_id' => $this->auth->id, 'default' => 1];

            // 地址
            $address = model('app\api\model\wanlshop\Address')
                ->where($where)
                ->field('id,name,mobile,province,city,district,address')
                ->find();
            // 判断地址是否存在
            if (!$address) {
                $this->error(__('请添加收货地址'));
            }

            // 订单数据
            $order = array();
            $map   = array();

            // 合计
            $statis = array(
                "allnum" => 0,
                "allsub" => 0
            );

            // 获取商品信息
            $goodInfo = $params['data'] ?? [];
            foreach ($goodInfo as $post) {
                $redis = Common::redis();

                // 商品id
                $goodsId = $post['goods_id'] ?? '';
                // 商品详情
                // 获取商品信息
                $goods = GoodsHandle::instance()->getGoodsInfo($goodsId);

                // 获取SKU
                $skuId = $post['sku_id'] ?? '';
                // 获取规格信息
                $sku = GoodsHandle::instance()->getGoodsSku($skuId);

                // 判断是否超出库存
                $sku_key = 'goods_' . $goodsId . '_' . $skuId;
                // 购买数量
                $number = $post['number'] ?? 1;
                // 记录购买数量
                if ($number > $redis->llen($sku_key)) {
                    $this->error("系统繁忙，请稍后抢购！");
                }

                // 获取快递及价格
                // 运费Id
                $freightId = $goods['freight_id'] ?? 0;
                // 支付费用
                $price = $sku['price'] ?? 0;
                // 获取运费信息
                $goods['freight'] = OrderHandle::instance()->getFreightPrice($freightId, $number, $price);

                // 获取SKU
                $goods['sku'] = $sku;
                // 数量
                // 购买数量
                $goods['number'] = $number;

                // 格式化
                if (empty($map[$goods['shop_id']])) {
                    $order[]                = array(
                        "shop_id"   => $goods['shop_id'],
                        "shop_name" => $goods->shop ? $goods->shop->visible(['shopname'])['shopname'] : [],
                        "products"  => [$goods],
                        "coupon"    => [],
                        "freight"   => [$goods['freight']],
                        "number"    => $number,
                        "sub_price" => bcmul($sku['price'], $number, 2)
                    );
                    $map[$goods['shop_id']] = $goods;
                } else {
                    // 追加1-*
                    foreach ($order as $key => $value) {
                        if ($value['shop_id'] == $goods['shop_id']) {
                            array_push($order[$key]['products'], $goods);
                            array_push($order[$key]['freight'], $goods['freight']);
                            $order[$key]['number']    += $post['number'];
                            $order[$key]['sub_price'] = bcadd($order[$key]['sub_price'], bcmul($sku['price'], $post['number'], 2), 2);
                            break;
                        }
                    }
                }
                // 所有店铺统计
                $statis['allnum'] += $number;
            }
            // 获取运费策略-店铺循环
            foreach ($order as $key => $value) {
                $config = model('app\api\model\wanlshop\ShopConfig')
                    ->where('shop_id', $value['shop_id'])
                    ->find();
                if ($config) {
                    if ($config['freight'] == 0) {
                        // 运费叠加
                        $order[$key]['freight'] = [
                            'id'    => $value['freight'][0]['id'],
                            'name'  => '运费叠加',
                            'price' => array_sum(array_column($value['freight'], 'price'))
                        ];
                    } else if ($config['freight'] == 1) {
                        // 以最低结算
                        array_multisort(array_column($value['freight'], 'price'), SORT_ASC, $value['freight']);
                        $order[$key]['freight'] = [
                            'id'    => $value['freight'][0]['id'],
                            'name'  => $value['freight'][0]['name'],
                            'price' => $value['freight'][0]['price']
                        ];
                    } else if ($config['freight'] == 2) {
                        // 以最高结算
                        array_multisort(array_column($value['freight'], 'price'), SORT_DESC, $value['freight']);
                        $order[$key]['freight'] = [
                            'id'    => $value['freight'][0]['id'],
                            'name'  => $value['freight'][0]['name'],
                            'price' => $value['freight'][0]['price']
                        ];
                    }
                    $order[$key]['order_price'] = $order[$key]['sub_price'];
                    // 获取总费用 根据总费用计算是否需要运费
                    $freight_prices = $this->freight($goods['freight_id'], $sku['weigh'], $post['number'], $address['city'], $order[$key]['order_price']);

                    // 2020年9月19日12:10:59 添加快递价格备份,用于还原运费
                    $order[$key]['freight_price_bak'] = $freight_prices;
                    $order[$key]['freight_price']     = $freight_prices->price;;
                    // 1.0.8升级
                    $order[$key]['sub_price'] = bcadd($order[$key]['sub_price'], $freight_prices->price, 2);
                    $statis['allsub']         = bcadd($statis['allsub'], $order[$key]['sub_price'], 2);
                } else {
                    $this->error(__('商家未配置运费组策略，暂不支持下单'));
                }
            }
            // 传递Token
            $datalist['token'] = Common::creatToken('orderToken_' . $this->auth->id);
            // 地址
            $datalist['addressData'] = $address;
            // 订单
            $datalist['orderData']['lists']  = $order;
            $datalist['orderData']['statis'] = $statis;
            $this->success('ok', $datalist);
        } else {
            $this->error(__('非法请求'));
        }
    }

    /**
     * 获取运费模板和子类 内部方法
     * 1.1.6升级
     * @param string $id 运费ID
     * @param string $weigh 商品重量
     * @param string $number 商品数量
     * @param string $city 邮递城市
     */
    private function freight($id = null, $weigh = null, $number = 0, $city = '南山区', $freight_prices = 0)
    {
        // 运费模板
        $data = model('app\api\model\wanlshop\ShopFreight')
            ->where('id', $id)->field('id,delivery,isdelivery,name,valuation')
            ->find();

        if (!$data) {
            $this->error(__('此商品运费模板不存在，暂不支持下单'));
        }

        $data['price'] = 0;
        // 是否包邮:0=自定义运费,1=卖家包邮
        if ($data['isdelivery'] == 0) {
            // 获取地址编码 1.1.0升级
            $list = model('app\api\model\wanlshop\ShopFreightData')
                ->where([
                    ['EXP', Db::raw('FIND_IN_SET(' . model('app\common\model\Area')->get(['name' => $city])->id . ', citys)')],
                    'freight_id' => $id
                ])
                ->find();
            // 查询是否存在运费模板数据
            if (!$list) {
                $list = model('app\api\model\wanlshop\ShopFreightData')->get(['freight_id' => $id]);
            }

            // 计价方式:0=按件数,1=按重量,2=按体积  1.0.2升级
            if ($data['valuation'] == 0) {
                if ($number <= $list['first']) {
                    $price = $list['first_fee'];
                } else {
                    $additional = $list['additional'] > 0 ? $list['additional'] : 1; //因为要更换vue后台，临时方案，为防止客户填写0
                    $price      = bcadd(bcmul(ceil(($number - $list['first']) / $additional), $list['additional_fee'], 2), $list['first_fee'], 2);
                }
            } else if ($data['valuation'] == 1 || $data['valuation'] == 2) {
                $weigh = $weigh * $number; // 订单总重量
                if ($weigh <= $list['first']) { // 如果重量小于等首重，则首重价格
                    $price = $list['first_fee'];
                } else {
                    $additional = $list['additional'] > 0 ? $list['additional'] : 1;
                    $price      = bcadd(bcmul(ceil(($weigh - $list['first']) / $additional), $list['additional_fee'], 2), $list['first_fee'], 2);
                }
            } else if ($data['valuation'] == 3) {
                // 如果订单总费用大于免配送费用，那么配送费为0，如果总费用小于等于免配送费用，需要运费
                if ($freight_prices > $list['first']) {
                    $price = 0;
                } else {
                    $price = $list['first_fee'];
                }
            }

            $data['price'] = $price;
        }
        return $data;
    }
}
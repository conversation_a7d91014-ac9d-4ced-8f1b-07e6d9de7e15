<?php

namespace app\api\controller\wanlshop\card;

use addons\wanlshop\library\WanlSdk\Common;
use app\api\library\CommonHandle;
use app\common\controller\Api;
use think\Exception;

/**
 * WanlShop分享接口
 */
class Share extends Api
{
    protected $noNeedLogin = [];
    protected $noNeedRight = ['*'];


    /**
     * 获取分享配置
     *
     * @ApiSummary  (WanlShop 分享接口获取分享配置)
     * @ApiMethod   (POST)
     *
     * @throws Exception
     */
    public function redeemCard()
    {
        if ($this->request->isPost()) {
            //设置过滤方法
            $this->request->filter(['strip_tags']);
            // 获取参数
            $params = $this->request->post();
            // 订单id
            $orderNo = $params['order_no'] ?? '';
            // 支付订单号
            $payNo = $params['pay_no'] ?? '';
            // 分享数量 num
            $num = $params['num'] ?? 0;
            // 分享人
            $userId = $this->auth->id;
            // 设置redis 5秒 锁
            $redis = Common::redis();
            $key   = PARAMS_INFO['share_cache_key'] . $userId;
            if ($redis->get($key)) {
                $this->error('请勿频繁操作');
            } else {
                $redis->set($key, $orderNo . $payNo . $userId, 5);
            }
            // 查询购物卡信息
            $payCardInfo = model('app\api\model\wanlshop\PayCard')
                ->where([
                    'pay_no'       => $payNo,
                    'order_no'     => $orderNo,
                    'user_id'      => $userId,
                    'supporter_id' => $userId,
                    'status'       => 'normal'
                ])
                ->find();

            $cardNum = $payCardInfo->total_number ?? 0;
            // 如果赠送的数量大于存储的数量 提示错误
            if ($num > $cardNum) {
                $this->error('待核销数量不足');
            }

            // 如果 num 不是10的倍数 且最少是10
            if ($num % 10 != 0 || $num < 10) {
                $this->error('数量必须是10的倍数');
            }

            $shareId = date('YmdHis') . rand(10000000, 99999999);
            // 如果赠送的数量小于等于核销的数量
            if ($num <= $cardNum) {
                // 核销数量
                // 分享列表
                $redeem_list = [
                    'share_id'     => $shareId,
                    'pay_no'       => $payCardInfo->pay_no ?? '',
                    'supporter_id' => '',
                    'user_id'      => $this->auth->id,
                    'shop_id'      => $payCardInfo->shop_id ?? 0,
                    'order_id'     => $payCardInfo->order_id ?? '',
                    'order_no'     => $payCardInfo->order_no ?? '',
                    'type'         => 'redeem',
                    'state'        => 0,
                    'number'       => $num,
                    'status'       => 'normal',
                    'difference'   => $payCardInfo->difference ?? ''
                ];
                // 保存分享信息
                // 开启实务 提交事务
                $model = model('app\api\model\wanlshop\share\PayRedeem');
                $model->startTrans();
                try {
                    // 保存分享信息
                    $model->save($redeem_list);                   // 保存分享信息
                    // 更新购物卡信息
                    $payCardInfo->total_number = $cardNum - $num; // 购物卡数量
                    // 如果数量都核销完毕，更新状态 state 变成 -1
                    if ($payCardInfo->total_number <= 0) {
                        $payCardInfo->state = 0;
                    }
                    $payCardInfo->save();                         // 保存购物卡信息
                    // 提交事务
                    $model->commit();
                } catch (\Exception $e) {
                    // 回滚事务
                    $model->rollback();
                    $this->error('核销失败');
                }
            }
            $this->success('返回成功', ['share_id' => $shareId]);
        }
    }

    /**
     * 领取赠送兑换卡
     *
     * @ApiSummary  (WanlShop 分享接口获取分享配置)
     * @ApiMethod   (POST)
     */
    public function redeemCardReceipt()
    {
        if ($this->request->isPost()) {
            //设置过滤方法
            $this->request->filter(['strip_tags']);
            // 获取参数
            $params = $this->request->post();
            // 分享id
            $shareId = $params['share_id'] ?? '';
            // 支付订单号
            $payNo = $params['pay_no'] ?? '';
            // 订单id
            $orderNo = $params['order_no'] ?? '';
            // 领取人
            $supporterId = $this->auth->id;
            // 查询分享信息
            // 获取赠送有效期
            // 获取wanlshop配置文件
            // 获取配置
            $config = get_addon_config('wanlshop');
            // 获取赠送有效期
            $validity = $config['order']['share_redeem'] ?? 0;
            // 获取有效期时间
            $validitySecondTime = (int)$validity * 24 * 60 * 60;
            // 查询分享信息
            $redeemInfo = model('app\api\model\wanlshop\share\PayRedeem')
                ->where([
                    'share_id' => $shareId,
                    'pay_no'   => $payNo,
                    'order_no' => $orderNo,
                    'status'   => 'normal'
                ])
                ->find();

            if ($redeemInfo) {
                // 获取赠送时间
                $shareTime = $redeemInfo->getData('createtime') ?? 0;
                if (time() - $shareTime > $validitySecondTime) {
                    $this->error('分享已过期');
                }

                // 自己不能领取
                if ($supporterId == $redeemInfo->user_id) {
                    $this->error('不能领取自己分享的兑换卡');
                }

                // 如果已经被领取过
                if ($redeemInfo->supporter_id != 0 || $redeemInfo->state == 1) {
                    $this->error('兑换卡已被领取');
                }

                if ($redeemInfo->state == 2) {
                    $this->error('兑换卡已被取消');
                }

                // 如果分享信息存在
                // 更新 supporter_id
                $redeemInfo->supporter_id = $supporterId;
                $redeemInfo->state        = 1;
                $redeemInfo->updatetime   = time();
                // 保存分享信息
                $redeemInfo->save();
                $this->success('领取成功', []);
            } else {
                $this->error('分享信息不存在');
            }
        }
    }

    /**
     * 获取领取订单信息
     * @throws \Exception
     */
    public function getRedeemOrderInfo()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        // 获取参数
        $params = $this->request->request();
        CommonHandle::instance()->checkRequiredKey($params, 'share_id', '非法请求');
        CommonHandle::instance()->checkRequiredKey($params, 'pay_no', '非法请求');
        CommonHandle::instance()->checkRequiredKey($params, 'order_no', '非法请求');
        // 赠送id
        $shareId = $params['share_id'] ?? '';
        // 支付订单号
        $payNo = $params['pay_no'] ?? '';
        // order_no
        $orderNo = $params['order_no'] ?? '';
        // 通过赠送id 获取订单信息
        // 首先获取赠送redeem 信息
        $redeem = model('app\api\model\wanlshop\share\PayRedeem')
            ->field('order_id,number,user_id,pay_no,order_no,share_id,supporter_id,state,createtime')
            ->with(['user' => function ($query) {
                $query->field('avatar, username, nickname');
            }])
            ->where([
                'share_id' => $shareId,
                'pay_no'   => $payNo,
                'order_no' => $orderNo
            ])
            ->find();

        // 如果赠送信息不存在
        if (!$redeem) {
            $this->error('非法请求');
        }

        // 获取订单信息
        // 获取订单id
        $id = $redeem->order_id ?? 0;
        // 购买用户id
        $userId = $redeem->user_id ?? 0;

        $order = model('app\api\model\wanlshop\Order')
            ->where([
                'id'      => $id,
                'user_id' => $userId
            ])
            ->field('id,shop_id')
            ->find();
        if (!$order) {
            $this->error('非法请求');
        }

        $data = [
            'share_id' => $redeem->share_id ?? '',
            'pay_no'   => $redeem->pay_no ?? '',
            'order_no' => $redeem->order_no ?? '',
            'state'    => $redeem->state ?? 0,
            // 获取店铺
            'shop'     => $order->shop ? $order->shop->visible(['shopname']) : [],
            // 获取订单商品
            'goods' => model('app\api\model\wanlshop\OrderGoods')
                ->with(['goodsSku'])
                ->where(['order_id' => $id])
                ->field('id,goods_id,goods_sku_id,title,difference,refund_id,refund_status')
                ->select(),
            // 通过order 获取兑换卡
            'redeem' => model('app\api\model\wanlshop\PayCard')
                ->where([
                    'order_id'     => $id,
                    'pay_no'       => $payNo,
                    'order_no'     => $orderNo,
                    'user_id'      => $userId,
                    'supporter_id' => $userId
                ])
                ->field('id, difference, total_number')
                ->find(),
            // 赠送人信息
            'user' => $redeem->user ? $redeem->user->visible(['avatar', 'username', 'nickname']) : [],
            'share_info' => $redeem ? $redeem->visible(['number', 'createtime']) : []
        ];

        // 赠送有效期
        // 获取配置
        $config = get_addon_config('wanlshop');
        // 获取赠送有效期
        $validity = $config['order']['share_redeem'] ?? 0;
        // 获取有效期时间
        $data['expiration_date'] = $validity;

        $this->success('ok', $data);
    }

    /**
     * 取消赠送兑换卡
     *
     * @ApiSummary  (WanlShop 分享接口获取分享配置)
     * @ApiMethod   (POST)
     */
    public function redeemCardCancel()
    {
        if ($this->request->isPost()) {
            //设置过滤方法
            $this->request->filter(['strip_tags']);
            // 获取参数
            $params = $this->request->post();
            // 分享id
            $shareId = $params['share_id'] ?? '';
            // 支付订单号
            $payNo = $params['pay_no'] ?? '';
            // 订单id
            $orderNo = $params['order_no'] ?? '';
            // 领取人
            $supporterId = $this->auth->id;
            // 查询分享信息
            // 获取赠送有效期
            // 查询分享信息
            $redeemInfo = model('app\api\model\wanlshop\share\PayRedeem')
                ->where([
                    'share_id' => $shareId,
                    'pay_no'   => $payNo,
                    'order_no' => $orderNo,
                    'status'   => 'normal'
                ])
                ->find();

            if ($redeemInfo) {
                // 自己不能领取
                if ($supporterId != $redeemInfo->user_id) {
                    $this->error('不能取消非自己分享的兑换卡');
                }

                if ($redeemInfo->state == 2) {
                    $this->error('兑换卡已被取消');
                }

                // 如果已经被领取过
                if ($redeemInfo->supporter_id != 0 || $redeemInfo->state == 1) {
                    $this->error('兑换卡已被领取, 不能撤销');
                }

                // 如果分享信息存在
                // 更新 supporter_id
                $redeemInfo->state        = 2;
                $redeemInfo->updatetime   = time();
                // 保存分享信息
                // 如果保存成功，恢复购物卡数量
                if ($redeemInfo->save()) {
                    $orderGoods = model('app\api\model\wanlshop\PayCard')
                        ->where([
                            'pay_no'       => $payNo,
                            'order_no'     => $orderNo,
                            'user_id'      => $supporterId,
                            'supporter_id' => $supporterId
                        ])
                        ->find();

                    // total_number + number
                    // 优化并发问题
                    // 给total_number 加锁
                    $orderGoods->total_number += $redeemInfo->number;
                    $orderGoods->save();

                    $this->success('撤销成功', []);
                }

            } else {
                $this->error('分享信息不存在');
            }
        }
    }
}
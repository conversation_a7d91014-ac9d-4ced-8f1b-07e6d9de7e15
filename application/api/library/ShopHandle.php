<?php

namespace app\api\library;

use Exception;

/**
 * 店铺配置
 */
class ShopHandle
{
    protected static $instance = null;

    /**
     * Class constructor
     *
     * @return    void
     */
    public function __construct()
    {
    }

    /**
     * Get instance
     * 单例
     *
     * @return    object
     */
    public static function instance($options = []): ?object
    {
        if (is_null(self::$instance)) {
            self::$instance = new static($options);
        }
        return self::$instance;
    }

    /**
     * 获取店铺配置
     * @param $shop_id
     * @return mixed
     */
    public function getShopConfig($shop_id)
    {
        return model('app\api\model\wanlshop\ShopConfig')
            ->where('shop_id', $shop_id)
            ->find();
    }

    /**
     * 获取用户是否使用优惠券
     * @param $coupon_id
     * @param $user_id
     * @param $shop_id
     * @return mixed
     */
    public function getUserCoupon($coupon_id, $user_id, $shop_id)
    {
        $res = model('app\api\model\wanlshop\CouponReceive')
            ->where(
                [
                    'id'      => $coupon_id,
                    'user_id' => $user_id,
                    'shop_id' => $shop_id
                ]
            )
            ->find();
        return $res;
    }

    /**
     * 获取分类ID列表
     * @param int $cacheTime 缓存时间
     * @return array
     * @throws \Throwable
     */
    public function getCategoryIds(int $cacheTime): array
    {
        $cacheKey = 'category_goods_nav_ids';

        // 尝试从缓存获取
        $categoryIds = cache($cacheKey);
        if ($categoryIds === false || $categoryIds === null) {
            // 缓存不存在，查询数据库
            $categoryIds = model('app\api\model\wanlshop\Category')
                ->where([
                    'type'   => 'goods',
                    'isnav'  => 1,
                    'status' => 'normal' // 建议添加状态检查
                ])
                ->column('id'); // 直接返回ID数组，更高效

            // 存入缓存
            if ($categoryIds) {
                cache($cacheKey, $categoryIds, $cacheTime);
            }
        }

        return $categoryIds ?: [];
    }

    /**
     * 获取商品基础数据
     * @throws \Throwable
     */
    public function getGoodsData(array $categoryIds, int $cacheTime): array
    {
        $cacheKey = 'goods_list_' . md5(serialize($categoryIds));

        // 尝试从缓存获取
        $goodsList = cache($cacheKey);
        if ($goodsList === false || $goodsList === null) {
            // 缓存不存在，查询数据库
            $goodsList = model('app\api\model\wanlshop\Goods')
                ->where([
                    'category_id' => ['in', $categoryIds],
                    'status'      => 'normal'
                ])
                ->field('id,image,title,price,shop_id,comment,sales,praise')
                ->order('id desc') // 建议添加排序，提高查询稳定性
                ->select();

            // 存入缓存（注意：关联数据可能无法正确缓存）
            // 建议对关联查询的结果不使用缓存，或者使用不同的缓存策略
            if ($goodsList) {
                cache($cacheKey, $goodsList, $cacheTime);
            }
        }

        return $goodsList ?: [];
    }

    /**
     * 获取店铺数据
     * @throws \Throwable
     */
    public function getShopData($goodsData, $cacheTime): array
    {
        $shopIds = array_unique(array_column($goodsData, 'shop_id'));
        if (empty($shopIds)) {
            return [];
        }

        $cacheKey = 'shop_basic_' . md5(implode(',', $shopIds));

        $data = cache($cacheKey);
        if ($data === false || $data === null) {
            $shops = model('app\api\model\wanlshop\Shop')
                ->where('id', 'in', $shopIds)
                ->field('id,state,shopname,isself')
                ->select();

            // 转换为以ID为键的关联数组
            $data = [];
            foreach ($shops as $shop) {
                $data[$shop['id']] = [
                    'state'    => $shop['state'],
                    'shopname' => $shop['shopname'],
                    'isself'   => $shop['isself']
                ];
            }

            if ($data) {
                cache($cacheKey, $data, $cacheTime);
            }
        }

        return $data ?: [];
    }

    /**
     * 获取直播数据
     * @throws \Throwable
     */
    public function getLiveData($goodsData, $cacheTime): array
    {
        $shopIds = array_unique(array_column($goodsData, 'shop_id'));
        if (empty($shopIds)) {
            return [];
        }

        $cacheKey = 'live_basic_' . md5(implode(',', $shopIds));

        $data = cache($cacheKey);
        if ($data === false || $data === null) {
            $data = model('app\api\model\wanlshop\Live')
                ->where([
                    'shop_id' => ['in', $shopIds],
                    'state'   => 1
                ])
                ->column('id', 'shop_id'); // 返回 [shop_id => live_id] 格式

            if ($data) {
                cache($cacheKey, $data, $cacheTime);
            }
        }

        return $data ?: [];
    }

    /**
     * 组装最终数据
     */
    public function assembleData($goodsData, $shopData, $liveData): array
    {
        $result = [];

        foreach ($goodsData as $goods) {
            $shopId = $goods['shop_id'] ?? 0;

            // 添加店铺信息
            $goods['shop'] = $shopData[$shopId] ?? ['state' => '', 'shopname' => ''];

            // 添加直播信息
            $goods['isLive'] = isset($liveData[$shopId]) ? ['id' => $liveData[$shopId]] : null;

            $result[] = $goods;
        }

        return $result;
    }
}
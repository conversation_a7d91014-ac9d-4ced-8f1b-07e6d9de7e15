<form id="add-form" class="form-horizontal form-ajax" role="form" data-toggle="validator" method="POST" action="">
    

    <div class="form-group">
        <label for="c-file" class="control-label col-xs-12 col-sm-2">{:__('File')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" name="row[file]" id="c-file" class="form-control"/>
            <ul class="row list-inline faupload-preview" id="p-file"></ul>
        </div>
    </div>

    <div class="form-group">
        <label for="c-file" class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="button" id="faupload-file" class="btn btn-success faupload" data-mimetype="image/*" data-params='{"category":"captcha"}' data-input-id="c-file" data-multiple="false" data-preview-id="p-file" data-url="{:url('ajax/upload')}"><i class="fa fa-upload"></i> {:__("Upload")}</button>
			<button type="button" id="fachoose-file" class="btn btn-primary fachoose" data-mimetype="image/*" data-params='{"category":"captcha"}' data-input-id="c-file" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button>
        </div>
    </div>

    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
<?php

namespace app\admin\model\user;

use think\Model;

class Binding extends Model
{
    // 表名
    protected $name = 'user_binding';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'status_text'
    ];

    public function getStatusList()
    {
        return ['normal' => __('Normal'), 'hidden' => __('Hidden')];
    }

    public function getStatusTextAttr($value, $data)
    {
        $value = $value ?: ($data['status'] ?? '');
        $list  = $this->getStatusList();
        return $list[$value] ?? '';
    }

    // user_id 关联
    public function userById()
    {
        return $this->belongsTo('app\admin\model\User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    // pid 关联
    public function userByPid()
    {
        return $this->belongsTo('app\admin\model\User', 'pid', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}

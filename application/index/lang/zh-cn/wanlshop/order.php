<?php

return [
    'Id'                    => 'ID',
    'User_id'               => '用户ID',
    'Shop_id'               => '所属商家',
    'Order_no'              => '订单号',
    'Address_id'            => '地址ID',
    'Express_no'            => '快递号',
    'State'                 => '订单状态',
    'State 1'               => '待支付',
    'State 2'               => '待发货',
    'State 3'               => '待收货',
    'State 4'               => '待评论',
    'State 5'               => '退款',
	'State 6'               => '已完成',
	'State 7'               => '已取消',
    'Createtime'            => '创建时间',
    'Paymenttime'           => '付款时间',
    'Delivertime'           => '发货时间',
    'Dealtime'              => '成交时间',
    'Updatetime'            => '更新时间',
    'Deletetime'            => '删除时间',
    'Status'                => '状态',
	'Order_type'                => '业务类型',
	'Order_type goods'          => '普通订单',
	'Order_type groups'         => '拼团订单',
	'Order_type seckill'        => '秒杀订单',
	// 追加
	'All'                 => '全部',
	'Import'              => '导入',
	'Recycle bin'         => '订单回收站',
	'Click to search %s'  => '点击搜索 %s',
	'Toggle menu visible' => '点击切换菜单显示',
	'Toggle sub menu'     => '点击切换子菜单显示',
	'Click to search %s'  => '点击搜索 %s',
	'Click to toggle'     => '点击切换',
	'Del'                 => '删除',
	'Drag to sort'        => '拖动进行排序',
	'User.username'			=> '买家',
	'User.nickname'			=> '买家',
	// 评论管理
	'States'                 => '评价类型',
	'States 0'               => '好评',
	'States 1'               => '中评',
	'States 2'               => '差评',
	'Content'               => '内容',
	'Tag'                   => '评论标签',
	'Suk'                   => '规格',
	'Images'                => '图片组',
	'Score'                 => '综合评分',
	'Score_describe'        => '描述相符',
	'Score_service'         => '服务相符',
	'Score_deliver'         => '发货相符',
	'Score_logistics'       => '物流相符',
	'Switch'                => '匿名评论',
	'goods.title'                => '商品',
	// 订单详情
	'Pay_type 0'  => '余额支付',
	'Pay_type 1'  => '微信支付',
	'Pay_type 2'  => '支付宝支付',
	'Pay_state 0' => '未支付',
	'Pay_state 1' => '已支付',
];

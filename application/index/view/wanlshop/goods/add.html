<style>
	#cxselect-example .form-group {
		margin: 10px 0;
	}
	[v-cloak] {display: none;}
	.wanl-specs .add-option{
		width: 250px;
		margin-bottom: 17px;
	}
	.wanl-specs .add-option .input-group-addon{
		color: #fff;
		background-color: #2c3e50;
	}
	.wanl-specs .panel .row{
		margin: 5px 0;
	}
	.wanl-specs .panel .remove {
		display: none;
		position: absolute;
		z-index: 2;
		width: 18px;
		height: 18px;
		font-size: 14px;
		line-height: 16px;
		color: #fff;
		text-align: center;
		cursor: pointer;
		background: rgba(0,0,0,.3);
		border-radius: 50%;
	}
	.wanl-specs .panel .panel-heading {
		position: relative;
	}
	.wanl-specs .panel .panel-heading .remove {
		right: 10px;
		top: 10px;
	}
	.wanl-specs .panel .panel-heading:hover .remove {
		display:block;
	}
	.wanl-specs .panel .wanl-specs-tag{
		background-color: #f8f8f8;
		position: relative;
		padding: 6px 10px;
		display: inline-block;
		margin-right: 10px;
		text-align: center;
		border-radius: 2px;
		cursor: pointer;
	}
	.wanl-specs .panel .wanl-specs-tag .remove {
		top: -5px;
		right: -5px;
	}
	.wanl-specs .panel .wanl-specs-tag:hover .remove{
		display:block;
	}
	
	.wanl-specs .batch .input-group{
		margin: 15px 0;
		width: 66%;
	}
	.sp_result_area{
		z-index: 10000;
	}
	.form-inline .form-control{
		padding-right: 100px;
	}
	.input-sm{
		padding: 5px 0;
		text-align: center;
	}
	.sp_container {
	    margin-right: 3px;
	}
	.panel-intro > .panel-heading {
	    position: sticky;
	    top: 0;
	    z-index: 9999;
	}
	.wanl-attribute {
	    display: flex;
	    flex-wrap: wrap;
	    background: #f5f5f5;
	    border: 1px solid #ddd;
		border-radius: 3px;
	}
	.wanl-attribute>div{
		width: 50%;
	}
	/* 1.0.8升级 */
	.wanl_sku_img{
		margin-bottom: 0;
		border: 1px solid #d2d6de;
	}
	.wanl_sku_img img{
		width: 28px;
		height: 28px;
	}
	.wanl_sku_img .skuUpload{
		width: 28px;
		height: 28px;
		line-height: 28px;
		text-align: center;
	}
	.wanl_sku_img .skuUpload i{
		color: #777;
	}
</style>
{if condition="($row.brand == 0) OR ($row.shopsort == 0) OR ($row.freight == 0) OR empty($row.config.sendPhoneNum) OR empty($row.config.returnPhoneNum)"}
<div class="alert alert-danger-light">
	<strong>暂不可发布商品：</strong>
	{in name="row.brand" value="0"}<p> 系统中没有任何品牌，请到【总后台】> 多用户商城 > 商品管理 > 品牌管理添加。</p>{/in} 
	{in name="row.shopsort" value="0"}<p> 尚未创建 “店铺分类”，请 <a class="btn-shopsort" href="#">点击创建</a>。</p>{/in}
	{in name="row.freight" value="0"}<p> 尚未创建 “运费模板”，请 <a class="btn-freight" href="#">点击创建</a>。</p>{/in}
	{empty name="row.config.sendPhoneNum"}<p> 尚未填写完整 “寄件人信息”，请 <a class="btn-send" href="#">点击填写</a> 寄件人信息。</p>{/empty}
	{empty name="row.config.returnPhoneNum"}<p> 尚未填写完整 “退货信息”，请 <a class="btn-return" href="#">点击填写</a> 退货信息。</p>{/empty}
</div>
{/if}
<div class="panel panel-default panel-intro">
	<div class="panel-heading">
		<ul class="nav nav-tabs">
			<li class="active"><a href="#basics">基础信息</a></li>
			<li><a href="#sale">销售信息</a></li>
			<li><a href="#wanlinfo">图文描述</a></li>
			<li><a href="#payment">支付信息</a></li>
			<li><a href="#logistics">物流信息</a></li>
			<li><a href="#service">售后信息</a></li>
		</ul>
	</div>
	<div class="panel-body">
		<div id="myTabContent" class="tab-content">
			<div class="tab-pane fade active in" id="one">
				<div class="widget-body no-padding" id="cxselect-example">
					<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
						
						<div id="app" v-cloak>
							<div class="row" id="basics">
								<div class="col-md-12">
									<div class="panel panel-default">
										<div class="panel-heading">基础信息</div>
										<div class="panel-body">
											<div class="form-group">
												<label class="control-label col-xs-12 col-sm-2">{:__('Title')}:</label>
												<div class="col-xs-12 col-sm-8">
													<input id="c-title" data-rule="required" class="form-control" name="row[title]" type="text"  placeholder="请输入宝贝标题">
												</div>
											</div>
											<div class="form-group">
											    <label class="control-label col-xs-12 col-sm-2">{:__('Category_id')}:</label>
											    <div class="col-xs-12 col-sm-8">
													<div class="form-inline">
														<select id="c-category_1" data-rule="required" v-model="categoryOne" class="form-control" @change="getCategory(1)">
														    <option :value="key" v-for="(item,key) in categoryList" :key="item.id">{{item.name}}</option>
														</select>
														<select id="c-category_2" data-rule="required" v-if="categoryOne != null && categoryList[categoryOne].childlist.length != 0" v-model="categoryTwo" class="form-control" @change="getCategory()">
														    <option :value="key" v-for="(item,key) in categoryList[categoryOne].childlist" :key="item.id">{{item.name}}</option>
														</select>
														<select id="c-category_3" data-rule="required" v-if="categoryTwo != null && categoryList[categoryOne].childlist[categoryTwo].childlist.length != 0" v-model="categoryThree" class="form-control" @change="getCategory()">
														    <option :value="key" v-for="(item,key) in categoryList[categoryOne].childlist[categoryTwo].childlist" :key="item.id">{{item.name}}</option>
														</select>
														<select id="c-category_4" data-rule="required" v-if="categoryThree != null && categoryList[categoryOne].childlist[categoryTwo].childlist[categoryThree].childlist.length != 0" v-model="categoryFour" class="form-control" @change="getCategory()">
														    <option :value="key" v-for="(item,key) in categoryList[categoryOne].childlist[categoryTwo].childlist[categoryThree].childlist" :key="item.id">{{item.name}}</option>
														</select>
														<select id="c-category_5" data-rule="required" v-if="categoryFour != null && categoryList[categoryOne].childlist[categoryTwo].childlist[categoryThree].childlist[categoryFour].childlist.length != 0" v-model="categoryFive" class="form-control" @change="getCategory()">
														    <option :value="key" v-for="(item,key) in categoryList[categoryOne].childlist[categoryTwo].childlist[categoryThree].childlist[categoryFour].childlist" :key="item.id">{{item.name}}</option>
														</select>
														<input class="form-control" name="row[category_id]" type="hidden" :value="categoryId">
													</div>
											    </div>
											</div>
											
											<div class="form-group">
											    <label class="control-label col-xs-12 col-sm-2">{:__('宝贝品牌')}:</label>
											    <div class="col-xs-12 col-sm-8">
													<div class="input-group">
														<input id="c-brand_id" data-rule="required" 
														data-source="wanlshop/brand/selectpage" 
														class="form-control selectpage" 
														name="row[brand_id]" 
														type="text" placeholder="点击获取品牌列表">
														<div class="input-group-addon no-border no-padding">
															<span><button type="button" class="btn btn-danger btn-brand" >{:__('申请品牌')}</button></span>
														</div>
													</div>
											    </div>
											</div>
											
											
											
											<div class="form-group" v-if="attributeData.length != 0">
												<label class="control-label col-xs-12 col-sm-2">{:__('类目属性')}:</label>
												<div class="col-xs-12 col-sm-8">
													<div class="wanl-attribute">
														<div class="form-group" v-for="(item,index) in attributeData" :key="index">
														  <label for="inputPassword" class="col-sm-3 control-label">{{item.name}}</label>
														  <div class="col-sm-9">
															<select :id="'c-attribute_' + item.name" :name="'row[attribute]['+item.name+']'" data-rule="required" class="form-control">
															    <option :value="data.name" v-for="(data,key) in item.value" :key="key">{{data.name}}</option>                                   
															</select>
														  </div>
														</div>
													</div>
												</div>
											</div>
											
											
											
										</div>
									</div>
									
								</div>
							</div>
							<div class="row" id="sale">
								<div class="col-md-12">
									<div class="panel panel-default">
										<div class="panel-heading">销售信息</div>
										<div class="panel-body">
											<div class="form-group wanl-specs">
												<label class="control-label col-xs-12 col-sm-2">{:__('产品属性')}:</label>
												<div class="col-xs-12 col-sm-8">
													<div class="input-group add-option">
														<input type="text" class="form-control" ref="specs-name" placeholder="多个产品属性以空格隔开">
														<span class="input-group-addon pointer" @click="spuAdd">新增属性</span>
													</div>
							
													<div class="panel panel-default" v-for="(item, i) in spu" :key="i">
														<header class="panel-heading">
															<b>{{item}}</b>
															<span class="remove" title="移除" @click="spuRemove(i)">×</span>
														</header>
														<div class="row">
															<div class="col-md-5 col-sm-5 col-xs-5">
																<div class="input-group">
																	<input type="text" class="form-control" :ref="'specs-name-' + i" placeholder="多规格以空格隔开">
																	<span class="input-group-addon pointer" @click="skuAdd(i)"><i class="fa fa-plus"></i></span>
																</div>
															</div>
															<div class="col-md-7 col-sm-7 col-xs-7">
																<div class="wanl-specs-tag" v-for="(v, j) in spuItem[i]" :key="j">
																	{{v}}<span class="remove" title="移除" @click="skuRemove(i, j)">×</span>
																</div>
															</div>
														</div>
													</div>
													<div>
														<div v-for="itemm in sku">
															<input type="hidden" name="row[sku][]" v-model="itemm" />
														</div>
														<input type="hidden" name="row[spu]" v-model="spu" />
														<div v-for="items in spuItem">
															<input type="hidden" name="row[spuItem][]" v-model="items" />
														</div>
													</div>
													<div class="row" v-show="sku.length">
														<div class="col-md-12">
															<table class="table table-bordered">
																<thead>
																	<tr class="info text-info">
																		<th width="80" v-for="item in spu"> {{item}} </th>
																		<th width="40">图片</th>
																		<th width="80">原价</th>
																		<th width="80">现价</th>
																		<th width="80">配送费</th>
																		<th width="100">一级分佣比例(100%)</th>
																		<th width="100">二级分佣比例(100%)</th>
																		<th width="80">库存</th>
																		<th width="60">重量(k)</th>
																		<th width="60">编码</th>
																	</tr>
																</thead>
																<tbody>
																	<tr v-for="(item, index) in sku" :key="index">
																		<td v-for="v in item">{{v}}</td>
																		<td>
																			<input type="hidden" name="row[thumbnail][]" v-model="skuImg[index]" v-if="skuImg[index]">
																			<input type="file" :id="'skuImg' + index" accept="image/*" @change="changeImage($event, index)" style="display:none" >
																			<label :for="'skuImg' + index" class="wanl_sku_img">
																				<img :src="cdnurl(skuImg[index])" v-if="skuImg[index]">
																				<div class="skuUpload" v-else>
																					<i class="fa fa-picture-o"></i>
																				</div>
																			</label>
																		</td>
																		<td> <input type="number" data-rule="required" name="row[market_price][]" class="input-sm form-control wanl-market_price"> </td>
																		<td> <input type="number" data-rule="required" name="row[price][]" class="input-sm form-control wanl-price"> </td>
																		<td> <input type="number" data-rule="required" name="row[delivery_fee][]" class="input-sm form-control wanl-delivery_fee"> </td>
																		<td> <input type="number" data-rule="required" name="row[commission_sharing][]" class="input-sm form-control wanl-commission_sharing"> </td>
																		<td> <input type="number" data-rule="required" name="row[second_commission_sharing][]" class="input-sm form-control wanl-second_commission_sharing"> </td>
																		<td> <input type="number" data-rule="required" name="row[stocks][]" class="input-sm form-control wanl-stock"></td>
																		<td> <input type="number" name="row[weigh][]" class="input-sm form-control wanl-weigh"></td>
																		<td> <input type="text" name="row[sn][]" class="input-sm form-control wanl-sn"></td>
																	</tr>
																</tbody>
															</table>
															<div @click="skuBatch()">
																<span v-if="batch == 0">
																	是否要批量设置？
																</span>
																<span v-else>
																	取消批量设置？
																</span>
															</div>
														</div>
													</div>
							
													<div class="batch" v-show="batch == 1">
														<div class="input-group">
															<div class="input-group-addon">原价 ￥</div>
															<input type="number" class="form-control" id="batch-market_price" placeholder="一键设置原价">
															<div class="input-group-addon" onclick="batchSet('market_price')">批量设置</div>
														</div>
														<div class="input-group">
															<div class="input-group-addon">现 ￥</div>
															<input type="number" class="form-control" id="batch-price" placeholder="一键设置现价">
															<div class="input-group-addon" onclick="batchSet('price')">批量设置</div>
														</div>
														<div class="input-group">
															<div class="input-group-addon">配送 ￥</div>
															<input type="number" class="form-control" id="batch-delivery_fee" placeholder="一键设置配送费">
															<div class="input-group-addon" onclick="batchSet('delivery_fee')">批量设置</div>
														</div>
														<div class="input-group">
															<div class="input-group-addon">一级分佣比例 ￥</div>
															<input type="number" class="form-control" id="batch-commission_sharing" placeholder="一键设置一级分佣比例(100%)">
															<div class="input-group-addon" onclick="batchSet('commission_sharing')">批量设置</div>
														</div>
														<div class="input-group">
															<div class="input-group-addon">二级分佣比例 ￥</div>
															<input type="number" class="form-control" id="batch-second_commission_sharing" placeholder="一键设置二级分佣比例(100%)">
															<div class="input-group-addon" onclick="batchSet('second_commission_sharing')">批量设置</div>
														</div>
														<div class="input-group">
															<div class="input-group-addon">库存</div>
															<input type="number" class="form-control" id="batch-stock" placeholder="一键设置库存">
															<div class="input-group-addon" onclick="batchSet('stock')">批量设置</div>
														</div>
														<div class="input-group">
															<div class="input-group-addon">重量</div>
															<input type="number" class="form-control" id="batch-weigh" placeholder="一键设置重量">
															<div class="input-group-addon" onclick="batchSet('weigh')">批量设置</div>
														</div>
														<div class="input-group">
															<div class="input-group-addon">编码</div>
															<input type="text" class="form-control" id="batch-sn" placeholder="一键设置编码">
															<div class="input-group-addon" onclick="batchSet('sn')">批量设置</div>
														</div>
													</div>
							
												</div>
											</div>
							
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="row" id="wanlinfo">
							<div class="col-md-12">
								<div class="panel panel-default">
									<div class="panel-heading">图文描述</div>
									<div class="panel-body">
										<div class="form-group">
											<label class="control-label col-xs-12 col-sm-2">{:__('Image')}:</label>
											<div class="col-xs-12 col-sm-8">
												<div class="input-group">
													<input id="c-image" data-rule="required" class="form-control" size="50" name="row[image]" type="text" placeholder="请上传宝贝主图">
													<div class="input-group-addon no-border no-padding">
														<span><button type="button" id="plupload-image" class="btn btn-danger plupload" data-input-id="c-image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
														<span><button type="button" id="fachoose-image" class="btn btn-primary fachoose" data-input-id="c-image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
													</div>
													<span class="msg-box n-right" for="c-image"></span>
												</div>
												<ul class="row list-inline plupload-preview" id="p-image"></ul>
											</div>
										</div>
										<div class="form-group">
											<label class="control-label col-xs-12 col-sm-2">{:__('Images')}:</label>
											<div class="col-xs-12 col-sm-8">
												<div class="input-group">
													<input id="c-images" data-rule="required" class="form-control" size="50" name="row[images]" type="text" placeholder="请上传宝贝相册">
													<div class="input-group-addon no-border no-padding">
														<span><button type="button" id="plupload-images" class="btn btn-danger plupload" data-input-id="c-images" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="true" data-preview-id="p-images"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
														<span><button type="button" id="fachoose-images" class="btn btn-primary fachoose" data-input-id="c-images" data-mimetype="image/*" data-multiple="true"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
													</div>
													<span class="msg-box n-right" for="c-images"></span>
												</div>
												<ul class="row list-inline plupload-preview" id="p-images"></ul>
											</div>
										</div>
										<div class="form-group">
										    <label class="control-label col-xs-12 col-sm-2">{:__('Description')}:</label>
										    <div class="col-xs-12 col-sm-8">
										        <input id="c-description" data-rule="required" class="form-control" name="row[description]" type="text" placeholder="请输入宝贝描述">
										    </div>
										</div>
										<div class="form-group">
											<label class="control-label col-xs-12 col-sm-2">{:__('Content')}:</label>
											<div class="col-xs-12 col-sm-8">
												<textarea id="c-content" data-rule="required" class="form-control editor" rows="5" name="row[content]" cols="50"></textarea>
											</div>
										</div>
										<div class="form-group">
											<label class="control-label col-xs-12 col-sm-2">{:__('Content Card')}:</label>
											<div class="col-xs-12 col-sm-8">
												<textarea id="c-content_card" class="form-control editor" rows="5" name="row[content_card]" cols="50"></textarea>
											</div>
										</div>
										<div class="form-group">
											<label class="control-label col-xs-12 col-sm-2">{:__('店铺中分类')}:</label>
											<div class="col-xs-12 col-sm-8">
												<div class="input-group">
													<input id="c-shop_category_id" data-rule="required"
													data-source="wanlshop/shopsort"
													data-params='{"isTree":1}'
													data-multiple="true"
													data-pagination="true"
													data-page-size="1"
													class="form-control selectpage"
													name="row[shop_category_id]"
													type="text"
													placeholder="点击选择店铺分类">
													<div class="input-group-addon no-border no-padding">
														<span><button type="button" class="btn btn-danger btn-shopsort" >{:__('新建分类')}</button></span>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="row" id="payment">
							<div class="col-md-12">
								<div class="panel panel-default">
									<div class="panel-heading">支付信息</div>
									<div class="panel-body">
										<div class="form-group">
										    <label class="control-label col-xs-12 col-sm-2">{:__('Stock')}:</label>
										    <div class="col-xs-12 col-sm-8">
										        <select  id="c-stock" data-rule="required" class="form-control selectpicker" name="row[stock]">
										            {foreach name="stockList" item="vo"}
										                <option value="{$key}">{$vo}</option>
										            {/foreach}
										        </select>
										    </div>
										</div>


									</div>
								</div>
							</div>
						</div>
						<div class="row" id="logistics">
							<div class="col-md-12">
								<div class="panel panel-default">
									<div class="panel-heading">物流信息</div>
									<div class="panel-body">
										<div class="form-group">
										    <label class="control-label col-xs-12 col-sm-2">{:__('Freight_id')}:</label>
										    <div class="col-xs-12 col-sm-8">
												<div class="input-group">
													<input id="c-freight_id" data-rule="required" data-source="wanlshop/freight" class="form-control selectpage" name="row[freight_id]" type="text" placeholder="点击选择运费模板">
													<div class="input-group-addon no-border no-padding">
														<span><button type="button" class="btn btn-danger btn-freight" >{:__('新建模板')}</button></span>
													</div>
												</div>
										    </div>
										</div>
									</div>
								</div>
							</div>
						</div>

						<div class="row" id="limit">
							<div class="col-md-12">
								<div class="panel panel-default">
									<div class="panel-heading">新用户注册</div>
									<div class="panel-body">
										<div class="form-group">
											<label class="control-label col-xs-12 col-sm-2">{:__('Login Limited')}:</label>
											<div class="col-xs-12 col-sm-8">
												<div class="radio">
													{foreach name="limitList" item="vo"}
													<label for="row[is_login_limited]-{$key}"><input id="row[is_login_limited]-{$key}" name="row[is_login_limited]" type="radio" value="{$key}" {in name="key" value="normal"}checked{/in} /> {$vo}</label>
													{/foreach}
												</div>
											</div>
										</div>
										<div class="form-group">
											<label class="control-label col-xs-12 col-sm-2">{:__('Limit Times')}:</label>
											<div class="col-xs-12 col-sm-8">
												<input id="c-limit_times" data-rule="required" class="form-control" name="row[limit_times]" type="text"  placeholder="请输入限购次数">
											</div>
										</div>
										<div class="form-group">
											<label class="control-label col-xs-12 col-sm-2">{:__('Limit Count')}:</label>
											<div class="col-xs-12 col-sm-8">
												<input id="c-limit_count" data-rule="required" class="form-control" name="row[limit_count]" type="text"  placeholder="请输入单词限购数量">
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="row" id="service">
							<div class="col-md-12">
								<div class="panel panel-default">
									<div class="panel-heading">售后信息</div>
									<div class="panel-body">
										<div class="form-group">
										    <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
										    <div class="col-xs-12 col-sm-8">
										        <div class="radio">
										        {foreach name="statusList" item="vo"}
										        <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="normal"}checked{/in} /> {$vo}</label> 
										        {/foreach}
										        </div>
										    </div>
										</div>
									</div>
								</div>
							</div>
						</div>

						<div class="form-group layer-footer">
							<div class="col-xs-12 col-sm-8">
								<button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
								<button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
							</div>
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>
</div>

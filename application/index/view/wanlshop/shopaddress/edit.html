<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Address_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-address_name" class="form-control" name="row[address_name]" type="text" value="{$row.address_name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('City')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class='control-relative'><input id="c-city" class="form-control" data-toggle="city-picker" name="row[city]" type="text" value="{$row.city|htmlentities}"></div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Street')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class='control-relative'><input id="c-street" class="form-control" data-toggle="addresspicker" name="row[street]" type="text" data-input-id="c-street" data-lng-id="c-lng" data-lat-id="c-lat" value="{$row.street|htmlentities}"></div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Lng')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-lng" class="form-control" name="row[longitude]" type="text" value="{$row.longitude|htmlentities}" readonly>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Lat')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-lat" class="form-control" name="row[latitude]" type="text" value="{$row.latitude|htmlentities}" readonly>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Delivery_distance')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-delivery_distance" class="form-control" step="0.01" name="row[delivery_distance]" type="number" value="{$row.delivery_distance|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>

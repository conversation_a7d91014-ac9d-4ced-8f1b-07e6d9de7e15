<?php

return [
    [
        'name'    => 'key',
        'title'   => '应用key',
        'type'    => 'string',
        'content' => [],
        'value'   => 'LTAI5tJuimM4odiGmoiL5MnB',
        'rule'    => 'required',
        'msg'     => '',
        'tip'     => '',
        'ok'      => '',
        'extend'  => '',
    ],
    [
        'name'    => 'secret',
        'title'   => '密钥secret',
        'type'    => 'string',
        'content' => [],
        'value'   => '******************************',
        'rule'    => 'required',
        'msg'     => '',
        'tip'     => '',
        'ok'      => '',
        'extend'  => '',
    ],
    [
        'name'    => 'sign',
        'title'   => '签名',
        'type'    => 'string',
        'content' => [],
        'value'   => '灵芝云',
        'rule'    => 'required',
        'msg'     => '',
        'tip'     => '',
        'ok'      => '',
        'extend'  => '',
    ],
    [
        'name'    => 'template',
        'title'   => '短信模板',
        'type'    => 'array',
        'content' => [],
        'value'   => [
            'register'     => 'SMS_480840050',
            'resetpwd'     => 'SMS_480855061',
            'changepwd'    => 'SMS_480845061',
            'changemobile' => 'SMS_480690075',
            'profile'      => 'SMS_479135272',
            'notice'       => 'SMS_479135272',
            'mobilelogin'  => 'SMS_480885052',
        ],
        'rule'    => 'required',
        'msg'     => '',
        'tip'     => '',
        'ok'      => '',
        'extend'  => '',
    ],
    [
        'name'    => '__tips__',
        'title'   => '温馨提示',
        'type'    => 'string',
        'content' => [],
        'value'   => '应用key和密钥你可以通过 https://ak-console.aliyun.com/?spm=a2c4g.11186623.2.13.fd315777PX3tjy#/accesskey 获取',
        'rule'    => 'required',
        'msg'     => '',
        'tip'     => '',
        'ok'      => '',
        'extend'  => '',
    ],
];

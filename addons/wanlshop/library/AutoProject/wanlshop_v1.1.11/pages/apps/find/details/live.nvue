<template>
	<div class="content">
		<div class="wanlive-bg">
			<image class="wanlive-bg-img" :lazy-load="true" :fade-show="false" :src="stcOss(liveData.image)"  mode="aspectFill"></image>
		</div>
		<div v-if="liveData.state == 1 || (liveData.state == 2 && playUrl)" class="wanlive-play">
			<wanl-live-play 
				:screenHeight="screenHeight" 
				:screenWidth="screenWidth" 
				:source="playUrl" 
			/>
		</div>
		<div v-else-if="liveData.state == 0" class="wanlive-tip">
			<wanl-live-tip text="直播正在准备中..."/>
		</div>
		<div v-else class="wanlive-tip">
			<wanl-live-tip text="直播转码中..."/>
		</div>
		<div class="wanlive-header" :style="{top:statusBarHeight + 'px'}">
			<wanl-live-header 
				:isFollow="liveData.isFollow" 
				:state="liveData.state" 
				:online="liveStatis.online"
				:shopdata="liveData.shop" 
				@change="onShopLike()" 
			/>
		</div>
		<!-- <div class="wanlive-bulletin" :style="{top: 0, left: 0}">
			<wanl-live-bulletin />
		</div> -->
		<div v-if="liveData.state == 1" class="wanlive-message" :style="{bottom:statusFootHeight + 60 +'px'}">
			<wanl-live-message ref="message" />
		</div>
		<div class="wanlive-footer" :style="{bottom:statusFootHeight +'px'}">
			<wanl-live-footer 
				:statusFootHeight="statusFootHeight" 
				:like="liveStatis.like" 
				:state="liveData.state"
				:goods="liveData.goods" 
				@change="sendLive($event)" 
			/>
		</div>
	</div>
</template>

<script>
	var system = uni.getSystemInfoSync();
	var statusFootHeight = 10;
	// #ifdef APP-PLUS || H5 || MP-WEIXIN
	statusFootHeight = system.safeAreaInsets.bottom;
	// #endif
	import {
		mapState
	} from 'vuex';
	import wanlLiveHeader from '@/components/wanl-live/header';
	import wanlLivePlay from '@/components/wanl-live/play';
	import wanlLiveTip from '@/components/wanl-live/tip';
	import wanlLiveMessage from '@/components/wanl-live/message';
	import wanlLiveBulletin from '@/components/wanl-live/bulletin';
	import wanlLiveFooter from '@/components/wanl-live/footer';
	export default {
		computed: {
			...mapState(['common'])
		},
		components: {
			wanlLiveHeader,
			wanlLivePlay,
			wanlLiveTip,
			wanlLiveMessage,
			wanlLiveBulletin,
			wanlLiveFooter
		},
		beforeCreate() {
			// #ifdef APP-NVUE
			var domModule = weex.requireModule('dom');
			domModule.addRule('fontFace', {
				fontFamily: 'iconfont',
				src: "url('/static/style/iconfont.ttf')"
			});
			// #endif
		},
		data() {
			return {
				statusBarHeight: system.statusBarHeight,
				statusFootHeight: statusFootHeight,
				screenHeight: system.screenHeight,
				screenWidth: system.screenWidth,
				playUrl: '',
				iSstart: false,
				// 直播间ID
				liveId: 0,
				// 直播参数
				liveData: {
					shop: {
						id: 0,
						avatar: '',
						shopname: '加载中请稍后',
					},
					state: 0,
					isFollow: false
				},
				// 实时统计
				liveStatis: {
					online: 1,
					like: 0
				}
			}
		},
		// 监听页面卸载
		onUnload() {
			this.pageUnload();
		},
		onLoad(option) {
			this.liveId = option.id;
			this.loadData();
			// 监听直播消息
			uni.$on('onLiveMessage', this.onMessage);
			// #ifdef MP-WEIXIN
			wx.showShareMenu({
				withShareTicket: true,
				menus: ['shareAppMessage', 'shareTimeline']
			});
			// #endif
		},
		methods: {
			async loadData() {
				await uni.request({
					url: '/wanlshop/live/play',
					data: {
						id: this.liveId
					},
					success: res => {
						let data = res.data;
						this.liveData = data;
						this.iSstart = true;
						this.liveStatis.like = data.like;
						if (data.state == 0 || data.state == 1) {
							let play = data.liveurl.split(',');
							// #ifdef APP-PLUS
							this.playUrl = play[0];
							// #endif
							// #ifndef APP-PLUS
							this.playUrl = play[1];
							// #endif
						} else if (data.state == 2) {
							this.playUrl = data.recordurl;
						}
					}
				});
			},
			// 接收直播群组消息
			onMessage(msg) {
				if (this.liveData.state == 1) {
					this.$nextTick(() => {
						this.$refs.message.onMessage(msg);
					});
					if (this.liveData.liveid == msg.group) {
						if (msg.message.type == 'update') {
							this.liveData.goods = msg.message.data;
						}
						if (msg.message.type == 'end') {
							uni.redirectTo({
								url: `/pages/page/end_live?id=${this.liveData.id}`
							});
						} else {
							this.liveStatis.like = msg.like;
							this.liveStatis.online = msg.online;
						}
					}
				}
			},
			//监听页面是否卸载，关闭播放，退出群组
			async pageUnload() {
				await uni.request({
					url: '/wanlshop/live/unload',
					data: {
						group: this.liveData.liveid,
						type: 'play'
					}
				});
			},
			async sendLive(e) {
				switch (e.type) {
					case 'msg':
						await uni.request({
							url: '/wanlshop/live/send',
							data: {
								message: e.message,
								group: this.liveData.liveid
							}
						});
						break;
					case 'like':
						this.liveStatis.like += 1;
						await uni.request({
							url: '/wanlshop/live/like',
							data: {
								id: this.liveId
							}
						});
						break;
					case 'seek':
						await uni.request({
							url: '/wanlshop/live/seek',
							data: {
								group: this.liveData.liveid,
								goods_index: e.key
							}
						});
						break;
				}
			},
			// 点击关注商家 & 用户
			async onShopLike() {
				this.liveData.isFollow = true;
				await uni.request({
					url: '/wanlshop/live/follow',
					data: {
						user_no: this.liveData.shop.find_user.user_no,
						group: this.liveData.liveid
					}
				});
			},
			stcOss(url) {
				let oss = this.$store.state.common.appUrl.oss;
				let image = '';
				if (url) {
					if (/^(http|https):\/\/.+/.test(url)) {
						image = url;
					} else {
						image = oss + url;
					}
				} else {
					image = '';
				}
				return image;
			},
			substrFirstMsgLine(item) {
				let nameLen = item.username.length;
				let conLen = item.content.length;
				if (nameLen > 13) {
					return item.username + ": ";
				} else {
					let syLen = 14 - nameLen;
					let content = item.content.substring(0, syLen);
					return item.username + ": " + content;
				}
			},
			ifMsgSecLine(item) {
				let nameLen = item.username.length;
				let conLen = item.content.length;
				if ((nameLen + conLen) > 15) {
					let syLen = 14 - nameLen;
					let content = item.content.substring(syLen, conLen);
					return content;
				} else {
					return false;
				}
			}
		}
	}
</script>

<style>
	@import '@/static/style/wanlnvue.css';
	/* 直播背景 */
	.wanlive-bg {
		position: fixed;
		background: #1c1e3d;
		z-index: 0;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
	}
		.wanlive-bg-img{
			width: 100%;
			height: 100%;
			/* #ifndef APP-PLUS */
			filter: blur(10px);
			/* #endif */
		}
		
	/* 直播播放	 */
	.wanlive-play {
		position: fixed;
		z-index: 0;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
	}
	
	/* 提示 */
	.wanlive-tip{
		position: fixed;
		z-index: 20;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
	}
	
	/* 导航栏 */
	.wanlive-header {
		position: fixed;
		z-index: 30;
		right: 0;
		left: 0;
	}
	
	/* 公告栏 */
	.wanlive-bulletin {
		position: fixed;
		z-index: 40;
		background: #f40;
	}

	/* 滚动消息 */
	.wanlive-message {
		position: fixed;
		z-index: 50;
		right: 0;
		left: 0;
	}
	
	/* 发送消息 */
	.wanlive-footer {
		position: fixed;
		z-index: 60;
		right: 0;
		left: 0;
	}
</style>

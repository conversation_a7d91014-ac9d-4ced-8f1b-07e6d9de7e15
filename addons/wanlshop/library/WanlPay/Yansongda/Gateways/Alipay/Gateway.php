<?php

namespace Wanl<PERSON>ay\Yansongda\Gateways\Alipay;

use <PERSON><PERSON><PERSON><PERSON>\Yansongda\Contracts\GatewayInterface;
use Wanl<PERSON>ay\Yansongda\Exceptions\InvalidArgumentException;
use Wanl<PERSON>ay\Supports\Collection;

abstract class Gateway implements GatewayInterface
{
    /**
     * Mode.
     *
     * @var string
     */
    protected $mode;

    /**
     * Bootstrap.
     *
     * <AUTHOR> <<EMAIL>>
     *
     * @throws InvalidArgumentException
     */
    public function __construct()
    {
        $this->mode = Support::getInstance()->mode;
    }

    /**
     * Pay an order.
     *
     * <AUTHOR> <<EMAIL>>
     *
     * @param string $endpoint
     *
     * @return Collection
     */
    abstract public function pay($endpoint, array $payload);
}

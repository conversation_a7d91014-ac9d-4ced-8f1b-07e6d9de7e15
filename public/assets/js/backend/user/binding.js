define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'user/binding/index' + location.search,
                    add_url: 'user/binding/add',
                    edit_url: 'user/binding/edit',
                    del_url: 'user/binding/del',
                    multi_url: 'user/binding/multi',
                    import_url: 'user/binding/import',
                    table: 'user_binding',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'pid', title: __('Pid')},
                        {field: 'userByPid.nickname', title: __('Leader Nickname'), operate: 'LIKE'},
                        {field: 'userByPid.username', title: __('Leader Username')},
                        {field: 'user_id', title: __('User_id')},
                        {field: 'userById.nickname', title: __('Nickname')},
                        {field: 'userById.username', title: __('Username')},
                        {
                            field: 'level',
                            title: __('Level'),
                            searchList: {0: __('LEVEL_ONE'), 1: __('LEVEL_TWO')},
                            formatter: Table.api.formatter.status
                        },
                        {
                            field: 'status',
                            title: __('Status'),
                            searchList: {"normal": __('Normal'), "hidden": __('Hidden')},
                            formatter: Table.api.formatter.status
                        },
                        {
                            field: 'createtime',
                            title: __('Createtime'),
                            operate: 'RANGE',
                            addclass: 'datetimerange',
                            autocomplete: false,
                            formatter: Table.api.formatter.datetime
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});

<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<title>IFrame playground</title>
</head>
<body>


<!-- Latest compiled and minified CSS -->
<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.1/css/bootstrap.min.css"/>


<!-- Latest Sortable -->
<script src="../../Sortable.js"></script>


<!-- Simple List -->
<div id="simpleList" class="list-group">
	<div class="list-group-item">This is <a href="http://rubaxa.github.io/Sortable/">Sortable</a></div>
	<div class="list-group-item">It works with Bootstrap...</div>
	<div class="list-group-item">...out of the box.</div>
	<div class="list-group-item">It has support for touch devices.</div>
	<div class="list-group-item">Just drag some elements around.</div>
</div>

<script>
	(function () {
		Sortable.create(simpleList, {group: 'shared'});


		var iframe = document.createElement('iframe');

		iframe.src = 'frame.html';
		iframe.width = '100%';
		iframe.onload = function () {
			var doc = iframe.contentDocument,
					list = doc.getElementById('listWithHandle');

			Sortable.create(list, {group: 'shared'});
		};


		document.body.appendChild(iframe);
	})();
</script>

</body>
</html>

<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="utf-8">
        <title>Date Range Picker for Bootstrap</title>
        <link rel="stylesheet" type="text/css" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.2/css/bootstrap.min.css" />
        <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.3.0/css/font-awesome.min.css">
        <script src="https://code.jquery.com/jquery-1.11.3.min.js"></script>
        <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.2/js/bootstrap.min.js"></script>
        <script src="moment.min.js"></script>         

        <script src="daterangepicker.js"></script>
        <link rel="stylesheet" type="text/css" href="daterangepicker.css" />

        <script src="website.js"></script>
        <link rel="stylesheet" type="text/css" href="website.css" />

        <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
        <!--[if lt IE 9]>
          <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
          <script src="https://oss.maxcdn.com/libs/respond.js/1.4.2/respond.min.js"></script>
        <![endif]-->

        <meta name="google-site-verification" content="1fP-Eo9i1ozV4MUlqZv2vsLv1r7tvYutUb6i38v0_vg" />
    </head>
    <body>

        <nav class="navbar navbar-inverse navbar-static-top">
            <div class="container">
                <ul class="nav navbar-nav">
                    <li>
                        <a href="http://www.daterangepicker.com" rel="nofollow" style="background: #eee; color: #000">
                            <span class="fa-stack">
                                <i class="fa fa-circle fa-stack-2x"></i>
                                <i class="fa fa-calendar fa-stack-1x fa-inverse" style="color: #eee"></i>
                            </span>
                            Date Range Picker
                        </a>
                    </li>
                </ul>
                <ul class="nav navbar-nav navbar-right">
                    <li>
                        <a href="https://www.improvely.com" rel="nofollow" style="background: #00caff">
                            <span class="fa-stack">
                                <i class="fa fa-circle fa-stack-2x"></i>
                                <i class="fa fa-signal fa-stack-1x fa-inverse" style="color: #00caff"></i>
                            </span>
                            Improvely
                        </a>
                    </li>
                    <li>
                        <a href="https://www.w3counter.com" rel="nofollow" style="background: #009fe8">
                            <span class="fa-stack">
                                <i class="fa fa-circle fa-stack-2x"></i>
                                <i class="fa fa-pie-chart fa-stack-1x fa-inverse" style="color: #009fe8"></i>
                            </span>
                            W3Counter
                        </a>
                    </li>
                    <li>
                        <a href="http://www.websitegoodies.com" rel="nofollow" style="background: #06c">
                            <span class="fa-stack">
                                <i class="fa fa-circle fa-stack-2x"></i>
                                <i class="fa fa-wrench fa-stack-1x fa-inverse" style="color: #06c"></i>
                            </span>
                            Website Goodies
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <div id="jumbo">
            <div class="container">
                <div class="row">
                    <div class="col-md-6">

                        <h1 style="margin: 0 0 20px 0">Date Range Picker</h1>
                        <p style="font-size: 18px; margin-bottom: 0">
                            A JavaScript component for choosing date ranges. 
                            <br />
                            Designed to work with the Bootstrap CSS framework.
                        </p>

                    </div>
                    <div class="col-md-6" style="text-align: right; padding-right: 0">

                        <a href="https://github.com/dangrossman/bootstrap-daterangepicker" class="btn btn-lg btn-default">View on GitHub</a>

                        &nbsp;

                        <a href="https://github.com/dangrossman/bootstrap-daterangepicker/archive/master.zip" class="btn btn-lg btn-success">Download ZIP</a>

                        <br /><br />

                        <iframe src="https://ghbtns.com/github-btn.html?user=dangrossman&repo=bootstrap-daterangepicker&type=star&count=true&size=large" frameborder="0" scrolling="0" width="160px" height="30px"></iframe>

                        <iframe src="https://ghbtns.com/github-btn.html?user=dangrossman&repo=bootstrap-daterangepicker&type=watch&count=true&size=large&v=2" frameborder="0" scrolling="0" width="160px" height="30px"></iframe>

                        <iframe src="https://ghbtns.com/github-btn.html?user=dangrossman&repo=bootstrap-daterangepicker&type=fork&count=true&size=large" frameborder="0" scrolling="0" width="160px" height="30px"></iframe>

                    </div>
                </div>
            </div>
        </div>

        <div class="container main">
            <div class="row">

                <div id="nav-spy">

                    <div id="sidebar">
                        <ul class="nav nav-pills nav-stacked">
                            <li><a href="#usage">Usage</a></li>
                            <li>
                                <a href="#examples">Examples</a>
                                <ul class="nav nav-pills nav-stacked" style="margin: 0 0 0 15px; padding: 0; font-size: 13px">
                                    <li><a href="#ex1">Date Range Picker</a></li>
                                    <li><a href="#ex2">Date and Time</a></li>
                                    <li><a href="#ex3">Single Date Picker</a></li>
                                    <li><a href="#ex4">Predefined Ranges</a></li>
                                    <li><a href="#ex5">Input Initially Empty</a></li>
                                </ul>
                            </li>
                            <li><a href="#config">Configuration Generator</a></li>
                            <li><a href="#options">Options, Methods &amp; Events</a></li>
                            <li><a href="#license">License</a></li>
                        </ul>

                        <script async src="//pagead2.googlesyndication.com/pagead/js/adsbygoogle.js"></script>
                        <!-- DRP Responsive -->
                        <ins class="adsbygoogle"
                             style="display:block"
                             data-ad-client="ca-pub-9095657276960731"
                             data-ad-slot="**********"
                             data-ad-format="auto"></ins>
                        <script>
                        (adsbygoogle = window.adsbygoogle || []).push({});
                        </script>
                    </div>

                </div>

                <div id="rightcol">

                    <div id="overview" name="overview">

                        <p>
                            Originally built for reporting at <a href="https://www.improvely.com">Improvely</a>,
                            the Date Range Picker can be attached to any webpage element to pop up two calendars
                            for selecting dates, times, or from predefined ranges like "Last 30 Days".
                        </p>

                        <a style="display: block; height: 300px; background: url('drp.png') top right no-repeat; background-size: cover; border: 1px solid #ccc; margin-bottom: 20px" href="https://awio.iljmp.com/5/drpdemo" title="Click for a Live Demo"></a>
                    
                    </div>

                    <div id="usage" name="usage">

                        <h2>Usage</h2>

                        <p>
                            Date Range Picker relies on <a href="http://getbootstrap.com/">Bootstrap</a>, <a href="http://www.jquery.com/">jQuery</a> and <a href="http://momentjs.com/">Moment.js</a>.

                            Include the required scripts and stylesheet in your page:
                        </p>

                        <script src="https://gist.github.com/dangrossman/fc0ec04cf073f0834c97.js"></script>

                        <p>Then attach the picker to the element you want to trigger it:</p>

                        <script src="https://gist.github.com/dangrossman/c4cc05fff6fe86cdca05.js"></script>

                        <hr />

                        <p>
                            You can customize Date Range Picker with <a href="#options">options</a>, and 
                            get notified when the user chooses new dates by providing a callback function.
                        </p>

                        <script src="https://gist.github.com/dangrossman/90b0b82275c81ac5904a.js"></script>

                    </div>

                    <div id="examples" name="examples">

                        <h2>Examples</h2>

                        <div id="ex1" name="ex1">

                            <h3>Date Range Picker</h3>

                            <p>
                                The Date Range Picker is attached to a text input. It will use the current
                                value of the input to initialize, and update the input if new dates are chosen.
                            </p>

                            <div class="row">
                                <div class="col-md-8 col-xs-12">
                                    <script src="https://gist.github.com/dangrossman/8e97e05e68cb683c4d71.js"></script>
                                </div>
                                <div class="col-md-4 col-xs-12">
                                    <h4>Demo:</h4>
                                    <input class="pull-right" type="text" name="daterange" value="01/15/2017 - 02/15/2017" />
                                </div>
                            </div>

                            <script type="text/javascript">
                            $(function() {
                                $('input[name="daterange"]').daterangepicker();
                            });
                            </script>

                        </div>

                        <div id="ex2" name="ex2">

                            <h3>Date and Time</h3>

                            <p>
                                The Date Range Picker can also be used to select times. Hour, minute and (optional)
                                second dropdowns are added below the calendars. An option exists to set the increment
                                count of the minutes dropdown to e.g. offer only 15-minute or 30-minute increments.
                            </p>

                            <div class="row">
                                <div class="col-md-7 col-xs-12">
                                    <script src="https://gist.github.com/dangrossman/89a40223f5a8a892fbf1.js"></script>
                                </div>
                                <div class="col-md-5 col-xs-12">
                                    <h4>Demo:</h4>
                                    <input class="pull-right" type="text" name="daterange2" value="01/01/2017 1:30 PM - 01/01/2017 2:00 PM" />
                                </div>
                            </div>

                            <script type="text/javascript">
                            $(function() {
                                $('input[name="daterange2"]').daterangepicker({
                                    timePicker: true,
                                    timePickerIncrement: 30,
                                    locale: {
                                        format: 'MM/DD/YYYY h:mm A'
                                    }
                                });
                            });
                            </script>

                        </div>

                        <div id="ex3" name="ex3">

                            <h3>Single Date Picker</h3>

                            <p>
                                The Date Range Picker can be turned into a single date picker widget with only
                                one calendar. In this example, dropdowns to select a month and year have also
                                been enabled at the top of the calendar to quickly jump to different months.
                            </p>

                            <div class="row">
                                <div class="col-md-9 col-xs-12">
                                    <script src="https://gist.github.com/dangrossman/df41977acae070b3feb2.js"></script>
                                </div>
                                <div class="col-md-3 col-xs-12">
                                    <h4>Demo:</h4>
                                    <input class="pull-right" type="text" name="birthdate" value="10/24/1984" />
                                </div>
                            </div>

                            <script type="text/javascript">
                            $(function() {
                                $('input[name="birthdate"]').daterangepicker({
                                    singleDatePicker: true,
                                    showDropdowns: true
                                }, 
                                function(start, end, label) {
                                    var years = moment().diff(start, 'years');
                                    alert("You are " + years + " years old.");
                                });
                            });
                            </script>

                        </div>

                        <div id="ex4" name="ex4">

                            <h3>Predefined Ranges</h3>

                            <p>
                                This example shows the option to predefine date ranges that
                                the user can choose from a list.
                            </p>

                            <div class="row">
                                <div class="col-md-8 col-xs-12">
                                    <script src="https://gist.github.com/dangrossman/bd8cf6efbba1c2123adc.js"></script>
                                </div>
                                <div class="col-md-4 col-xs-12">
                                    <h4>Demo:</h4>
                                    <div id="reportrange" class="pull-right" style="background: #fff; cursor: pointer; padding: 5px 10px; border: 1px solid #ccc; width: 100%">
                                        <i class="glyphicon glyphicon-calendar fa fa-calendar"></i>&nbsp;
                                        <span></span> <b class="caret"></b>
                                    </div>
                                </div>
                            </div>

            			    <script type="text/javascript">
                            $(function() {

                                var start = moment().subtract(29, 'days');
                                var end = moment();

                                function cb(start, end) {
                                    $('#reportrange span').html(start.format('MMMM D, YYYY') + ' - ' + end.format('MMMM D, YYYY'));
                                }

                                $('#reportrange').daterangepicker({
                                    startDate: start,
                                    endDate: end,
                                    ranges: {
                                       'Today': [moment(), moment()],
                                       'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                                       'Last 7 Days': [moment().subtract(6, 'days'), moment()],
                                       'Last 30 Days': [moment().subtract(29, 'days'), moment()],
                                       'This Month': [moment().startOf('month'), moment().endOf('month')],
                                       'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
                                    }
                                }, cb);

                                cb(start, end);

                            });
                            </script>

                        </div>

                        <div id="ex5" name="ex5">

                            <h3>Input Initially Empty</h3>

                            <p>
                                If you're using a date range as a filter, you may want to attach a picker to an 
                                input but leave it empty by default. This example shows how to accomplish that
                                using the <code>autoUpdateInput</code> setting, and the <code>apply</code> and 
                                <code>cancel</code> events.
                            </p>

                            <div class="row">
                                <div class="col-md-9 col-xs-12">
                                    <script src="https://gist.github.com/dangrossman/de22909c4d24f3f3508c.js"></script>
                                </div>
                                <div class="col-md-3 col-xs-12">
                                    <h4>Demo:</h4>
                                    <input class="pull-right" type="text" name="datefilter" value="" />
                                </div>
                            </div>


                        </div>

                        <script type="text/javascript">
                        $(function() {

                            $('input[name="datefilter"]').daterangepicker({
                                autoUpdateInput: false,
                                locale: {
                                    cancelLabel: 'Clear'
                                }
                            });

                            $('input[name="datefilter"]').on('apply.daterangepicker', function(ev, picker) {
                                $(this).val(picker.startDate.format('MM/DD/YYYY') + ' - ' + picker.endDate.format('MM/DD/YYYY'));
                            });

                            $('input[name="datefilter"]').on('cancel.daterangepicker', function(ev, picker) {
                                $(this).val('');
                            });

                        });
                        </script>

                    </div>



                    <div id="config" name="config">

                        <h2>Configuration Generator</h2>

                        <div class="well configurator">
                                           
                          <form>
                          <div class="row">

                            <div class="col-md-4">

                              <div class="form-group">
                                <label for="parentEl">parentEl</label>
                                <input type="text" class="form-control" id="parentEl" value="" placeholder="body">
                              </div>

                              <div class="form-group">
                                <label for="startDate">startDate</label>
                                <input type="text" class="form-control" id="startDate" value="07/01/2017">
                              </div>

                              <div class="form-group">
                                <label for="endDate">endDate</label>
                                <input type="text" class="form-control" id="endDate" value="07/15/2017">
                              </div>

                              <div class="form-group">
                                <label for="minDate">minDate</label>
                                <input type="text" class="form-control" id="minDate" value="" placeholder="MM/DD/YYYY">
                              </div>

                              <div class="form-group">
                                <label for="maxDate">maxDate</label>
                                <input type="text" class="form-control" id="maxDate" value="" placeholder="MM/DD/YYYY">
                              </div>

                              <div class="form-group">
                                <label for="opens">opens</label>
                                <select id="opens" class="form-control">
                                  <option value="right" selected>right</option>
                                  <option value="left">left</option>
                                  <option value="center">center</option>
                                </select>
                              </div>

                              <div class="form-group">
                                <label for="drops">drops</label>
                                <select id="drops" class="form-control">
                                  <option value="down" selected>down</option>
                                  <option value="up">up</option>
                                </select>
                              </div>

                            </div>
                            <div class="col-md-4">

                              <div class="checkbox">
                                <label>
                                  <input type="checkbox" id="showDropdowns"> showDropdowns
                                </label>
                              </div>

                              <div class="checkbox">
                                <label>
                                  <input type="checkbox" id="showWeekNumbers"> showWeekNumbers
                                </label>
                              </div>

                              <div class="checkbox">
                                <label>
                                  <input type="checkbox" id="showISOWeekNumbers"> showISOWeekNumbers
                                </label>
                              </div>

                              <div class="checkbox">
                                <label>
                                  <input type="checkbox" id="singleDatePicker"> singleDatePicker
                                </label>
                              </div>

                              <div class="checkbox">
                                <label>
                                  <input type="checkbox" id="timePicker"> timePicker
                                </label>
                              </div>

                              <div class="checkbox">
                                <label>
                                  <input type="checkbox" id="timePicker24Hour"> timePicker24Hour
                                </label>
                              </div>

                              <div class="form-group">
                                <label for="timePickerIncrement">timePickerIncrement (in minutes)</label>
                                <input type="text" class="form-control" id="timePickerIncrement" value="1">
                              </div>

                              <div class="checkbox">
                                <label>
                                  <input type="checkbox" id="timePickerSeconds"> timePickerSeconds
                                </label>
                              </div>

                              <div class="checkbox">
                                <label>
                                  <input type="checkbox" id="dateLimit"> dateLimit (with example date range span)
                                </label>
                              </div>

                              <div class="checkbox">
                                <label>
                                  <input type="checkbox" id="locale"> locale (with example settings)
                                </label>
                              </div>

                              <div class="checkbox">
                                <label>
                                  <input type="checkbox" id="autoApply"> autoApply
                                </label>
                              </div>

                              <div class="checkbox">
                                <label>
                                  <input type="checkbox" id="linkedCalendars" checked="checked"> linkedCalendars
                                </label>
                              </div>

                              <div class="checkbox">
                                <label>
                                  <input type="checkbox" id="autoUpdateInput" checked="checked"> autoUpdateInput
                                </label>
                              </div>

                            </div>
                            <div class="col-md-4">

                              <div class="form-group">
                                <label for="buttonClasses">buttonClasses</label>
                                <input type="text" class="form-control" id="buttonClasses" value="btn btn-sm">
                              </div>

                              <div class="form-group">
                                <label for="applyClass">applyClass</label>
                                <input type="text" class="form-control" id="applyClass" value="btn-success">
                              </div>

                              <div class="form-group">
                                <label for="cancelClass">cancelClass</label>
                                <input type="text" class="form-control" id="cancelClass" value="btn-default">
                              </div>

                              <div class="checkbox">
                                <label>
                                  <input type="checkbox" id="ranges"> ranges (with example predefined ranges)
                                </label>
                              </div>

                              <div class="checkbox">
                                <label>
                                  <input type="checkbox" id="alwaysShowCalendars"> alwaysShowCalendars
                                </label>
                              </div>

                              <div class="checkbox">
                                <label>
                                  <input type="checkbox" id="showCustomRangeLabel" checked="checked"> showCustomRangeLabel
                                </label>
                              </div>

                            </div>

                          </div>
                          </form>

                        </div>

                        <div class="row">

                          <div class="col-md-4 col-md-offset-2 demo">
                            <h4>Your Date Range Picker</h4>
                            <input type="text" id="config-demo" class="form-control">
                            <i class="glyphicon glyphicon-calendar fa fa-calendar"></i>
                          </div>

                          <div class="col-md-6">
                            <h4>Configuration</h4>

                            <div class="well">
                              <textarea id="config-text" style="height: 300px; width: 100%; padding: 10px"></textarea>
                            </div>
                          </div>

                        </div>

                    </div>

                    <div id="options" name="options">

                        <h2>Options</h2>

                        <ul class="nobullets">
                            <li>
                                <code>startDate</code> (Date object, moment object or string) The start of the initially selected date range
                            </li>
                            <li>
                                <code>endDate</code>: (Date object, moment object or string) The end of the initially selected date range
                            </li>
                            <li>
                                <code>minDate</code>: (Date object, moment object or string) The earliest date a user may select
                            </li>
                            <li>
                                <code>maxDate</code>: (Date object, moment object or string) The latest date a user may select
                            </li>
                            <li>
                                <code>dateLimit</code>: (object) The maximum span between the selected start and end dates. Can have any property you can add to a moment object (i.e. days, months)
                            </li>
                            <li>
                                <code>showDropdowns</code>: (boolean) Show year and month select boxes above calendars to jump to a specific month and year
                            </li>
                            <li>
                                <code>showWeekNumbers</code>: (boolean) Show localized week numbers at the start of each week on the calendars
                            </li>
                            <li>
                                <code>showISOWeekNumbers</code>: (boolean) Show ISO week numbers at the start of each week on the calendars
                            </li>
                            <li>
                                <code>timePicker</code>: (boolean) Allow selection of dates with times, not just dates
                            </li>
                            <li>
                                <code>timePickerIncrement</code>: (number) Increment of the minutes selection list for times (i.e. 30 to allow only selection of times ending in 0 or 30)
                            </li>
                            <li>
                                <code>timePicker24Hour</code>: (boolean) Use 24-hour instead of 12-hour times, removing the AM/PM selection
                            </li>
                            <li>
                                <code>timePickerSeconds</code>: (boolean) Show seconds in the timePicker
                            </li>
                            <li>
                                <code>ranges</code>: (object) Set predefined date ranges the user can select from. Each key is the label for the range, and its value an array with two dates representing the bounds of the range
                            </li>
                            <li>
                                <code>showCustomRangeLabel</code>: (boolean) Displays an item labeled "Custom Range" at
                                the end of the list of predefined ranges, when the <code>ranges</code> option is used.
                                This option will be highlighted whenever the current date range selection does not match
                                one of the predefined ranges. Clicking it will display the calendars to select a new range.
                            </li>
                            <li>
                                <code>alwaysShowCalendars</code>: (boolean) Normally, if you use the <code>ranges</code>
                                option to specify pre-defined date ranges, calendars for choosing a custom date range are not shown until the user clicks "Custom Range". When this option is set to true, the calendars for choosing a custom date range are always shown instead.
                            </li>
                            <li>
                                <code>opens</code>: (string</code>: 'left'/'right'/'center') Whether the picker appears aligned to the left, to the right, or centered under the HTML element it's attached to
                            </li>
                            <li>
                                <code>drops</code>: (string</code>: 'down' or 'up') Whether the picker appears below (default) or above the HTML element it's attached to
                            </li>
                            <li>
                                <code>buttonClasses</code>: (array) CSS class names that will be added to all buttons in the picker
                            </li>
                            <li>
                                <code>applyClass</code>: (string) CSS class string that will be added to the apply button
                            </li>
                            <li>
                                <code>cancelClass</code>: (string) CSS class string that will be added to the cancel button
                            </li>
                            <li>
                                <code>locale</code>: (object) Allows you to provide localized strings for buttons and labels, customize the date format, and change the first day of week for the calendars.
                                Check off "locale (with example settings)" in the configuration generator to see how
                                to customize these options.
                            </li>
                            <li>
                                <code>singleDatePicker</code>: (boolean) Show only a single calendar to choose one date, instead of a range picker with two calendars; the start and end dates provided to your callback will be the same single date chosen
                            </li>
                            <li>
                                <code>autoApply</code>: (boolean) Hide the apply and cancel buttons, and automatically apply a new date range as soon as two dates or a predefined range is selected
                            </li>
                            <li>
                                <code>linkedCalendars</code>: (boolean) When enabled, the two calendars displayed will always be for two sequential months (i.e. January and February), and both will be advanced when clicking the left or right arrows above the calendars. When disabled, the two calendars can be individually advanced and display any month/year.
                            </li>
                            <li>
                                <code>isInvalidDate</code>: (function) A function that is passed each date in the two
                                calendars before they are displayed, and may return true or false to indicate whether
                                that date should be available for selection or not.
                            </li>
                            <li>
                                <code>isCustomDate</code>: (function) A function that is passed each date in the two
                                calendars before they are displayed, and may return a string or array of CSS class names
                                to apply to that date's calendar cell.
                            </li>
                            <li>
                                <code>autoUpdateInput</code>: (boolean) Indicates whether the date range picker should
                                automatically update the value of an <code>&lt;input&gt;</code> element it's attached to
                                at initialization and when the selected dates change.
                            </li>
                            <li>
                                <code>parentEl</code>: (string) jQuery selector of the parent element that the date range picker will be added to, if not provided this will be 'body'
                            </li>
                        </ul>

                    </div>

                    <div id="methods" name="methods">

                        <h2>Methods</h2>

                        <p>
                            You can programmatically update the <code>startDate</code> and <code>endDate</code>
                            in the picker using the <code>setStartDate</code> and <code>setEndDate</code> methods.
                            You can access the Date Range Picker object and its functions and properties through 
                            data properties of the element you attached it to.
                        </p>

                        <script src="https://gist.github.com/dangrossman/8ff9b1220c9b5682e8bd.js"></script>

                        <br />
                        
                        <ul class="nobullets">
                            <li>
                                <code>setStartDate(Date/moment/string)</code>: Sets the date range picker's currently selected start date to the provided date
                            </li>
                            <li>
                                <code>setEndDate(Date/moment/string)</code>: Sets the date range picker's currently selected end date to the provided date
                            </li>
                        </ul>

                        <p style="margin: 0"><b>Example usage:</b></p>

                        <script src="https://gist.github.com/dangrossman/e1a8effbaeacb50a1e31.js"></script>

                    </div>

                    <div id="events" name="events">

                        <h2>Events</h2>

                        <p>
                            Several events are triggered on the element you attach the picker to, which you can listen for.
                        </p>

                        <ul class="nobullets">
                            <li>
                                <code>show.daterangepicker</code>: Triggered when the picker is shown
                            </li>
                            <li>
                                <code>hide.daterangepicker</code>: Triggered when the picker is hidden
                            </li>
                            <li>
                                <code>showCalendar.daterangepicker</code>: Triggered when the calendar(s) are shown
                            </li>
                            <li>
                                <code>hideCalendar.daterangepicker</code>: Triggered when the calendar(s) are hidden
                            </li>
                            <li>
                                <code>apply.daterangepicker</code>: Triggered when the apply button is clicked,
                                or when a predefined range is clicked
                            </li>
                            <li>
                                <code>cancel.daterangepicker</code>: Triggered when the cancel button is clicked
                            </li>
                        </ul>

                        <p>
                            Some applications need a "clear" instead of a "cancel" functionality, which can be achieved by changing the button label and watching for the cancel event:
                        </p>

                        <script src="https://gist.github.com/dangrossman/1bea78da703f2896564d.js"></script>

                        <br />

                        <p>
                            While passing in a callback to the constructor is the easiest way to listen for changes in the selected date range, you can also do something every time the apply button is clicked even if the selection hasn't changed:
                        </p>

                        <script src="https://gist.github.com/dangrossman/0c6c911fea1459b5fd13.js"></script>

                    </div>

                    <div id="license" name="license">

                        <h2>License</h2>

                        <p>The MIT License (MIT)</p>

                        <p>Copyright (c) 2012-2017 <a href="http://www.dangrossman.info">Dan Grossman</a></p>

                        <p>
                            Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
                        </p>

                        <p>
                            The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
                        </p>

                        <p>
                            THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
                        </p>

                    </div>

                    <div id="comments">

                        <h2>Comments</h2>

                        <div id="disqus_thread"></div>
                        <script type="text/javascript">
                            /* * * CONFIGURATION VARIABLES: EDIT BEFORE PASTING INTO YOUR WEBPAGE * * */
                            var disqus_url = 'http://www.dangrossman.info/2012/08/20/a-date-range-picker-for-twitter-bootstrap/';
                            var disqus_identifier = '1045 http://www.dangrossman.info/?p=1045';
                            var disqus_container_id = 'disqus_thread';
                            var disqus_shortname = 'dangrossman';
                            var disqus_title = "A Date Range Picker for Bootstrap";

                            /* * * DON'T EDIT BELOW THIS LINE * * */
                            (function() {
                                var dsq = document.createElement('script'); dsq.type = 'text/javascript'; dsq.async = true;
                                dsq.src = '//' + disqus_shortname + '.disqus.com/embed.js';
                                (document.getElementsByTagName('head')[0] || document.getElementsByTagName('body')[0]).appendChild(dsq);
                            })();
                        </script>
                        <noscript>Please enable JavaScript to view the <a href="https://disqus.com/?ref_noscript">comments powered by Disqus.</a></noscript>

                    </div>

                </div>

            </div>
        </div>

        <!-- Begin W3Counter Tracking Code -->
        <script type="text/javascript" src="https://www.w3counter.com/tracker.js?id=90840"></script>
        <!-- End W3Counter Tracking Code -->

        <script type="text/javascript">
        var im_domain = 'awio';
        var im_project_id = 48;
        (function(e,t){window._improvely=[];var n=e.getElementsByTagName("script")[0];var r=e.createElement("script");r.type="text/javascript";r.src="https://"+im_domain+".iljmp.com/improvely.js";r.async=true;n.parentNode.insertBefore(r,n);if(typeof t.init=="undefined"){t.init=function(e,t){window._improvely.push(["init",e,t])};t.goal=function(e){window._improvely.push(["goal",e])};t.conversion=function(e){window._improvely.push(["conversion",e])};t.label=function(e){window._improvely.push(["label",e])}}window.improvely=t;t.init(im_domain,im_project_id)})(document,window.improvely||[])
        </script>

	<script>
	!function(e){window._wsg=7;var t=e.getElementsByTagName("script")[0],s=e.createElement("script");s.type="text/javascript",s.src="https://www.websitegoodies.com/js/widgets.js",t.parentNode.insertBefore(s,t)}(document);
	</script>

        <div id="footer">
            Copyright &copy; 2012-2017 <a href="http://www.awio.com">Awio Web Services LLC</a>.
            &nbsp;
            Developed and maintained by <a href="http://www.dangrossman.info/">Dan Grossman</a>. 
            &nbsp;
        </div>

    </body>
</html>

<!DOCTYPE HTML>
<html>
<head>
<meta charset="UTF-8">
<title>compile-demo</title>
<script src="../dist/template.js"></script>
</head>

<body>
<h1>在javascript中存放模板</h1>
<div id="content"></div>
<script>
var source = '<ul>'
+    '{{each list as value i}}'
+        '<li>索引 {{i + 1}} ：{{value}}</li>'
+    '{{/each}}'
+ '</ul>';

var render = template.compile(source);
var html = render({
    list: ['摄影', '电影', '民谣', '旅行', '吉他']
});

document.getElementById('content').innerHTML = html;
</script>
</body>
</html>
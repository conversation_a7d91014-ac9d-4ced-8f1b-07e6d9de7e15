body {
    padding-top: 70px;
}

ul.nav li.main {
    font-weight: bold;
}

div.col-md-3 {
    padding-left: 0;
}

div.source-links {
    float: right;
}

/*
 * Side navigation
 *
 * Scrollspy and affixed enhanced navigation to highlight sections and secondary
 * sections of docs content.
 */

/* By default it's not affixed in mobile views, so undo that */
.bs-sidebar {
  overflow-y: scroll;
  max-height: 86%;
  overflow-x: hidden;
}

.bs-sidebar.affix {
    position: static;
}

.bs-sidebar.well {
    padding: 0;
}

/* First level of nav */
.bs-sidenav {
    margin-top: 30px;
    margin-bottom: 30px;
    padding-top:    10px;
    padding-bottom: 10px;
    border-radius: 5px;
}

/* All levels of nav */
.bs-sidebar .nav > li > a {
    display: block;
    padding: 5px 20px;
}
.bs-sidebar .nav > li > a:hover,
.bs-sidebar .nav > li > a:focus {
    text-decoration: none;
    border-right: 1px solid;
}
.bs-sidebar .nav > .active > a,
.bs-sidebar .nav > .active:hover > a,
.bs-sidebar .nav > .active:focus > a {
    font-weight: bold;
    background-color: transparent;
    border-right: 1px solid;
}

/* Nav: second level (shown on .active) */
.bs-sidebar .nav .nav {
    display: none; /* Hide by default, but at >768px, show it */
    margin-bottom: 8px;
}
.bs-sidebar .nav .nav > li > a {
    padding-top:    3px;
    padding-bottom: 3px;
    padding-left: 30px;
    font-size: 90%;
}

/* Show and affix the side nav when space allows it */
@media (min-width: 992px) {
    .bs-sidebar .nav > .active > ul {
        display: block;
    }
    /* Widen the fixed sidebar */
    .bs-sidebar.affix,
    .bs-sidebar.affix-bottom {
        width: 213px;
    }
    .bs-sidebar.affix {
        position: fixed; /* Undo the static from mobile first approach */
        top: 80px;
    }
    .bs-sidebar.affix-bottom {
        position: absolute; /* Undo the static from mobile first approach */
    }
    .bs-sidebar.affix-bottom .bs-sidenav,
    .bs-sidebar.affix .bs-sidenav {
        margin-top: 0;
        margin-bottom: 0;
    }
}
@media (min-width: 1200px) {
    /* Widen the fixed sidebar again */
    .bs-sidebar.affix-bottom,
    .bs-sidebar.affix {
        width: 263px;
    }
}